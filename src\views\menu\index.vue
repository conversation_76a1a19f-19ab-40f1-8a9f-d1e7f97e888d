<script lang="ts" setup>
import { ref, onMounted, h } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NTag, NSpace, NIcon, useMessage, NDataTable } from 'naive-ui';
import { fetchMenuTree } from '@/service/api';

defineOptions({
  name: 'MenuManagement'
});

const message = useMessage();

// 菜单数据
const menuData = ref<Api.Menu.Response[]>([]);
const loading = ref(false);

// 菜单类型映射
const menuTypeMap = {
  1: { label: '菜单', type: 'info' as const },
  2: { label: '按钮', type: 'warning' as const }
};

// 状态映射
const statusMap = {
  1: { label: '启用', type: 'success' as const },
  0: { label: '禁用', type: 'error' as const }
};

// 表格列定义
const columns: DataTableColumns<Api.Menu.Response> = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center'
  },
  {
    title: '菜单名称',
    key: 'name',
    width: 200,
    ellipsis: {
      tooltip: true
    },
    tree: true
  },
  {
    title: '菜单编码',
    key: 'code',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '路径',
    key: 'path',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '图标',
    key: 'icon',
    width: 100,
    align: 'center',
    render(row) {
      return row.icon ? h('div', { class: 'flex justify-center' }, [
        h(NIcon, { size: 18 }, {
          default: () => h('i', { class: `icon-${row.icon}` })
        })
      ]) : '-';
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 100,
    align: 'center',
    render(row) {
      const typeInfo = menuTypeMap[row.type as keyof typeof menuTypeMap];
      return typeInfo ? h(NTag, { type: typeInfo.type }, { default: () => typeInfo.label }) : '-';
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      const statusInfo = statusMap[row.status as keyof typeof statusMap];
      return statusInfo ? h(NTag, { type: statusInfo.type }, { default: () => statusInfo.label }) : '-';
    }
  },
  {
    title: '排序',
    key: 'sort',
    width: 80,
    align: 'center'
  },
  {
    title: '层级',
    key: 'level',
    width: 80,
    align: 'center'
  },
  {
    title: '权限标识',
    key: 'permission',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    render(row) {
      return h(NSpace, { justify: 'center' }, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleEdit(row)
          }, {
            default: () => '编辑'
          }),
          h(NButton, {
            size: 'small',
            type: 'error',
            ghost: true,
            onClick: () => handleDelete(row)
          }, {
            default: () => '删除'
          })
        ]
      });
    }
  }
];

// 获取菜单树数据
const getMenuTree = async () => {
  try {
    loading.value = true;
    const { data, error } = await fetchMenuTree();
    console.log('data', data);
    if (!error && data) {
      menuData.value = data;
    } else {
      message.error('获取菜单数据失败');
    }
  } catch (error) {
    console.error('获取菜单数据失败:', error);
    message.error('获取菜单数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const handleRefresh = () => {
  getMenuTree();
};

// 新增菜单
const handleAdd = () => {
  message.info('新增菜单功能待实现');
};

// 编辑菜单
const handleEdit = (row: Api.Menu.Response) => {
  message.info(`编辑菜单: ${row.name}`);
};

// 删除菜单
const handleDelete = (row: Api.Menu.Response) => {
  message.warning(`删除菜单: ${row.name}`);
};



// 组件挂载时获取数据
onMounted(() => {
  getMenuTree();
});
</script>

<template>
  <div class="h-full flex-col-stretch gap-16px overflow-hidden">
    <!-- 页面标题和操作按钮 -->
    <div class="flex-y-center justify-between">
      <div>
        <h2 class="text-18px font-bold">菜单管理</h2>
        <p class="text-14px text-gray-500 mt-4px">管理系统菜单和权限配置</p>
      </div>

      <NSpace>
        <NButton type="primary" @click="handleAdd">
          新增菜单
        </NButton>

        <NButton @click="handleRefresh" :loading="loading">
          刷新
        </NButton>
      </NSpace>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1-hidden">
      <NDataTable
        :columns="columns"
        :data="menuData"
        :loading="loading"
        :scroll-x="1400"
        :row-key="(row: Api.Menu.Response) => row.id || 0"
        :children-key="'children'"
        class="h-full"
        flex-height
        striped
        bordered
      />
    </div>
  </div>
</template>

<style lang="scss">

</style>
