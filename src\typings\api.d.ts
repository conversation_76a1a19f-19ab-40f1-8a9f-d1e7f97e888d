/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    /** 用户角色枚举 */
    enum UserRole {
      /** 普通用户 */
      AUser = 'a_user',
      /** 管理员 */
      Admin = 'admin',
      /** 采购员 */
      Purchaser = 'purchaser'
    }
    /** 登录请求参数 */
    interface LoginParams {
      phone: string;
      password: string;
    }

    /** 登录响应数据 */
    interface LoginToken {
      /** 访问token */
      token: string;
      /** 刷新token */
      refreshToken: string;
    }

    /** 用户信息 */
    interface UserInfo {
      /** 用户ID */
      id?: number;
      /** 用户名 */
      username?: string;
      /** 手机号 */
      phone?: string;
      /** 用户角色 */
      role?: UserRole;
      /** 用户状态 */
      status?: number;
      /** 创建时间 */
      created_at?: string;
      /** 更新时间 */
      updated_at?: string;
    }

    /** 刷新token请求参数 */
    interface RefreshTokenParams {
      refreshToken: string;
    }

    /** 注册请求参数 */
    interface RegisterParams {
      /** 手机号 */
      phone: string;
      /** 密码 */
      password: string;
      /** 用户名 */
      username: string;
      /** 用户角色 */
      role: string;
    }

    /** 注册响应数据 */
    interface RegisterResult {
      /** 注册是否成功 */
      success: boolean;
      /** 消息 */
      message?: string;
    }

    /** 发送短信验证码请求参数 */
    interface SendSmsParams {
      /** 手机号 */
      phone: string;
    }

    /** 发送短信验证码响应数据 */
    interface SendSmsResult {
      /** 发送是否成功 */
      success: boolean;
      /** 消息 */
      message?: string;
    }

    /** 短信验证码登录请求参数 */
    interface SmsLoginParams {
      /** 手机号 */
      phone: string;
      /** 验证码 */
      code: string;
      /** 用户名 */
      username: string;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace Menu
   *
   * backend api module: "menu"
   */
  namespace Menu {
    /** 菜单树响应 */
    interface Response {
      /** 响应状态码 */
      code?: number;
      /** 响应数据 */
      data?: MenuTree[];
      /** 响应消息 */
      message?: string;
      [property: string]: any;
    }

    /** 菜单树节点 */
    interface MenuTree {
      children?: MenuTree[];
      code?: string;
      component?: string;
      created_at?: string;
      description?: string;
      hidden?: boolean;
      icon?: string;
      id?: number;
      keep_alive?: boolean;
      level?: number;
      name?: string;
      parent_id?: number;
      path?: string;
      permission?: string;
      redirect?: string;
      sort?: number;
      status?: number;
      type?: number;
      updated_at?: string;
      [property: string]: any;
    }
  }

  /**
   * namespace Order
   *
   * backend api module: "order"
   */
  namespace Order {
    /** 订单状态枚举 */
    enum OrderStatus {
      /** 待处理 */
      Pending = 'pending',
      /** 处理中 */
      Processing = 'processing',
      /** 已采购 */
      Purchased = 'purchased',
      /** 已发货 */
      Shipped = 'shipped',
      /** 已完成 */
      Completed = 'completed',
      /** 已取消 */
      Cancelled = 'cancelled'
    }

    /** 用户角色枚举 */
    enum UserRole {
      /** 普通用户 */
      AUser = 'a_user',
      /** 管理员 */
      Admin = 'admin',
      /** 采购员 */
      Purchaser = 'purchaser'
    }

    /** 文件上传信息 */
    interface FileUpload {
      id?: number;
      file_name?: string;
      file_path?: string;
      file_size?: number;
      file_type?: string;
      record_id?: number;
      created_at?: string;
      updated_at?: string;
    }

    /** 用户信息 */
    interface User {
      id?: number;
      phone: string;
      role: UserRole;
      status?: number;
      username: string;
      created_at?: string;
      updated_at?: string;
    }

    /** 采购记录 */
    interface PurchaseRecord {
      id?: number;
      order_id?: number;
      b_order_no?: string;
      purchase_amount?: number;
      purchaser_id?: number;
      purchaser?: User;
      logistics_no?: string;
      status?: OrderStatus;
      remark?: string;
      files?: FileUpload[];
      created_at?: string;
      updated_at?: string;
    }

    /** 订单信息 */
    interface Order {
      id?: number;
      order_no?: string;
      platform_order_no?: string;
      user_id?: number;
      user?: User;
      purchaser_id?: number;
      purchaser?: User;
      buyer_name?: string;
      receiver_name?: string;
      receiver_phone?: string;
      receiver_address?: string;
      receiver_city?: string;
      receiver_state?: string;
      postal_code?: string;
      product_id?: string;
      product_name?: string;
      product_image_url?: string;
      product_price?: number;
      product_quantity?: number;
      product_specs?: string;
      asin_sku?: string;
      order_amount?: number;
      order_message?: string;
      payment_time?: string;
      ship_deadline?: string;
      shop_account?: string;
      status?: OrderStatus;
      remark?: string;
      purchase_record?: PurchaseRecord;
      created_at?: string;
      updated_at?: string;
    }

    /** 获取订单列表请求参数 */
    interface GetOrderListParams {
      /** 页码 */
      page?: number;
      /** 每页数量 */
      page_size?: number;
      /** 订单号 */
      order_no?: string;
      /** 订单状态 */
      status?: string;
      /** 用户ID */
      user_id?: number;
      /** 采购员ID */
      purchaser_id?: number;
    }

    /** 获取订单列表响应数据 */
    interface GetOrderListResponse {
      list?: Order[];
      page?: number;
      page_size?: number;
      total?: number;
    }

    /** 通用响应结构 */
    interface Response<T = any> {
      /** 响应状态码 */
      code?: number;
      /** 响应数据 */
      data?: T;
      /** 响应消息 */
      message?: string;
    }
  }
}
