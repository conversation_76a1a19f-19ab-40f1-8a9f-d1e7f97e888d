import { request } from '../request';

/**
 * 获取订单列表
 *
 * @param params 查询参数
 */
export function fetchOrderList(params?: Api.Order.GetOrderListParams) {
  return request<Api.Order.GetOrderListResponse>({
    url: '/api/orders',
    method: 'get',
    params
  });
}

/**
 * 获取订单详情
 *
 * @param id 订单ID
 */
export function fetchOrderDetail(id: number) {
  return request<Api.Order.Order>({
    url: `/api/orders/${id}`,
    method: 'get'
  });
}

/**
 * 删除订单
 *
 * @param id 订单ID
 */
export function deleteOrder(id: number) {
  return request({
    url: `/api/orders/${id}`,
    method: 'delete'
  });
}

/**
 * 取消订单
 *
 * @param id 订单ID
 */
export function cancelOrder(id: number) {
  return request({
    url: `/api/orders/${id}/cancel`,
    method: 'put'
  });
}

/**
 * 创建订单
 *
 * @param data 订单数据
 */
export function createOrder(data: Partial<Api.Order.Order>) {
  return request<Api.Order.Response<Api.Order.Order>>({
    url: '/api/orders',
    method: 'post',
    data
  });
}

/**
 * 更新订单
 *
 * @param id 订单ID
 * @param data 订单数据
 */
export function updateOrder(id: number, data: Partial<Api.Order.Order>) {
  return request<Api.Order.Response<Api.Order.Order>>({
    url: `/api/orders/${id}`,
    method: 'put',
    data
  });
}



/**
 * 批量更新订单状态
 *
 * @param ids 订单ID列表
 * @param status 新状态
 */
export function batchUpdateOrderStatus(ids: number[], status: Api.Order.OrderStatus) {
  return request<Api.Order.Response<null>>({
    url: '/api/orders/batch-status',
    method: 'put',
    data: { ids, status }
  });
}

/**
 * 下载订单导入模板
 */
export function downloadTemplate() {
  return request<Blob, 'blob'>({
    url: '/api/excel/template',
    method: 'get',
    responseType: 'blob'
  });
}
