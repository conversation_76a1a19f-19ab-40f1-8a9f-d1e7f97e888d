<script lang="ts" setup>
import { h, reactive, ref, onMounted } from 'vue';
import { useBoolean } from '@sa/hooks';
import { useOrderManagement, useOrderStatus } from '@/hooks/business/order';
import { downloadTemplate } from '@/service/api/order';
import { NButton } from 'naive-ui';

defineOptions({
  name: 'OrderManagement'
});

// 使用订单管理hook
const {
  loading,
  orderList,
  pagination,
  searchParams,
  getOrderList,
  searchOrders,
  resetSearch,
  handlePageChange,
  handlePageSizeChange,
  filterByStatus
} = useOrderManagement();

// 使用订单状态hook
const { getStatusInfo } = useOrderStatus();

// 标签页状态
const activeTab = ref('all');

// 搜索表单数据
interface SearchForm {
  orderSubmitTimeRange: [number, number] | null;
  customerPaymentTimeRange: [number, number] | null;
  deliveryDeadlineRange: [number, number] | null;
  expectedArrivalChinaRange: [number, number] | null;
  expectedArrivalUSRange: [number, number] | null;
  productName: string;
  proxyProductASIM: string;
  businessPlatformOrderNumber: string;
  trackingNumber: string;
  sufengOrderNumber: string;
  orderMessage: string;
  orderTags: string[];
  bOrderNumber: string;
  hasBOrderNumber: string;
  storeName: string;
}

const searchForm = reactive<SearchForm>({
  orderSubmitTimeRange: null,
  customerPaymentTimeRange: null,
  deliveryDeadlineRange: null,
  expectedArrivalChinaRange: null,
  expectedArrivalUSRange: null,
  productName: '',
  proxyProductASIM: '',
  businessPlatformOrderNumber: '',
  trackingNumber: '',
  sufengOrderNumber: '',
  orderMessage: '',
  orderTags: [],
  bOrderNumber: '',
  hasBOrderNumber: '',
  storeName: ''
});

// 筛选条件展开/收起状态
const { bool: searchExpanded, toggle: toggleSearchExpanded } = useBoolean(true);

// 弹出框控制
const { bool: modalVisible, setFalse: closeModal } = useBoolean();
const { bool: importModalVisible, setTrue: openImportModal, setFalse: closeImportModal } = useBoolean();

// 订单数据类型（使用API类型）
type OrderData = Api.Order.Order;

// 标签页选项
const tabOptions = [
  { key: 'all', label: '全部' },
  { key: 'pending_order', label: '待拍单' },
  { key: 'ordering', label: '拍单中' },
  { key: 'ordered', label: '已拍单' },
  { key: 'completed', label: '已完成' },
  { key: 'invalid', label: '已失效' }
];

// 订单标签配置（10种颜色）
const orderTagOptions = [
  { label: '紧急', value: 'urgent', color: 'error' },
  { label: 'VIP', value: 'vip', color: 'warning' },
  { label: '普通', value: 'normal', color: 'info' },
  { label: '折扣', value: 'discount', color: 'success' },
  { label: '预售', value: 'presale', color: 'primary' },
  { label: '补发', value: 'reissue', color: 'tertiary' },
  { label: '退换', value: 'return', color: 'error' },
  { label: '特殊', value: 'special', color: 'warning' },
  { label: '海外', value: 'overseas', color: 'info' },
  { label: '定制', value: 'custom', color: 'success' }
];

// 是否有B单号选项
const hasBOrderNumberOptions = [
  { label: '全部', value: '' },
  { label: '是', value: 'true' },
  { label: '否', value: 'false' }
];

// 批量设置标签状态
const { bool: batchTagModalVisible, setTrue: openBatchTagModal, setFalse: closeBatchTagModal } = useBoolean();
const selectedOrderIds = ref<string[]>([]);
const batchSelectedTags = ref<string[]>([]);

// 批量上传订单抽屉状态
const { bool: batchUploadDrawerVisible, setTrue: openBatchUploadDrawer } = useBoolean();

// 批量上传标签页状态
const batchUploadActiveTab = ref('success');

// 批量上传订单数据类型
interface BatchUploadOrderData {
  id: string;
  businessPlatformInfo: string;
  buyerInfo: string;
  proxyProductInfo: string;
  orderMessage: string;
  failureReason?: string;
}

// 识别成功的订单数据
const successOrderData = ref<BatchUploadOrderData[]>([
  {
    id: '1',
    businessPlatformInfo: '淘宝 - 天猫旗舰店',
    buyerInfo: '张三 - 138****1234',
    proxyProductInfo: 'iPhone 15 Pro 256GB 深空黑色',
    orderMessage: '客户要求加急处理'
  },
  {
    id: '2',
    businessPlatformInfo: '京东 - 京东自营',
    buyerInfo: '李四 - 139****5678',
    proxyProductInfo: 'MacBook Pro 14英寸 M3芯片',
    orderMessage: ''
  }
]);

// 识别失败的订单数据
const failureOrderData = ref<BatchUploadOrderData[]>([
  {
    id: '3',
    businessPlatformInfo: '拼多多 - 品牌官方店',
    buyerInfo: '王五 - 136****9012',
    proxyProductInfo: 'AirPods Pro 第二代',
    orderMessage: '包邮',
    failureReason: '商品信息不完整'
  }
]);

// 识别成功表格列配置
const successColumns = [
  {
    title: '经营平台信息',
    key: 'businessPlatformInfo',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '买家信息',
    key: 'buyerInfo',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '代拍商品信息',
    key: 'proxyProductInfo',
    width: 250,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '订单留言',
    key: 'orderMessage',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  }
];

// 识别失败表格列配置
const failureColumns = [
  {
    title: '原因',
    key: 'failureReason',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '经营平台信息',
    key: 'businessPlatformInfo',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '买家信息',
    key: 'buyerInfo',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '代拍商品信息',
    key: 'proxyProductInfo',
    width: 250,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '订单留言信息',
    key: 'orderMessage',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  }
];

// 分页配置已在useOrderManagement中定义

// 表格列定义
const columns = [
  {
    type: 'selection' as const,
    width: 50
  },
  {
    title: '订单号',
    key: 'order_no',
    width: 140,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '商品名称',
    key: 'product_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: 'ASIN/SKU',
    key: 'asin_sku',
    width: 140,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '平台订单号',
    key: 'platform_order_no',
    width: 160,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '买家姓名',
    key: 'buyer_name',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '订单状态',
    key: 'status',
    width: 100,
    render: (row: OrderData) => {
      const statusInfo = getStatusInfo(row.status || '');
      return h('NTag', { type: statusInfo.type as any, size: 'small' }, statusInfo.text);
    }
  },
  {
    title: '订单金额',
    key: 'order_amount',
    width: 120,
    render: (row: OrderData) => {
      return row.order_amount ? `¥${row.order_amount.toFixed(2)}` : '-';
    }
  },
  {
    title: 'B单号',
    key: 'purchase_record',
    width: 140,
    ellipsis: {
      tooltip: true
    },
    render: (row: OrderData) => {
      return row.purchase_record?.b_order_no || '-';
    }
  },
  {
    title: '采购员',
    key: 'purchaser',
    width: 100,
    render: (row: OrderData) => {
      return row.purchaser?.username || '-';
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    render: (row: OrderData) => {
      return row.created_at ? new Date(row.created_at).toLocaleString() : '-';
    }
  },
  {
    title: '操作',
    key: 'operate',
    width: 200,
    fixed: 'right' as const,
    render: (row: OrderData) => {
      return h('div', { class: 'flex gap-8px flex-wrap' }, [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleView(row.id)
          },
          '查看'
        ),
        h(
          'NButton',
          {
            size: 'small',
            type: 'info',
            ghost: true,
            onClick: () => handleEdit(row.id)
          },
          '编辑'
        ),
        h(
          'NButton',
          {
            size: 'small',
            type: 'warning',
            ghost: true,
            onClick: () => handleShip(row.id)
          },
          '发货'
        ),
        h(
          'NButton',
          {
            size: 'small',
            type: 'error',
            ghost: true,
            onClick: () => handleCancel(row.id)
          },
          '取消'
        )
      ]);
    }
  }
];

// 标签页切换
function handleTabChange(value: string) {
  activeTab.value = value;
  filterByStatus(value);
}

// 搜索
function handleSearch() {
  const params: Partial<Api.Order.GetOrderListParams> = {
    order_no: searchForm.sufengOrderNumber || undefined,
    status: activeTab.value === 'all' ? undefined : activeTab.value
  };
  searchOrders(params);
}

// 重置搜索
function handleReset() {
  Object.keys(searchForm).forEach(key => {
    (searchForm as any)[key] = '';
  });
  resetSearch();
}

// 新增订单
function handleAddOrder() {
  openBatchUploadDrawer();
}

// 批量导入
function handleBatchImport() {
  openImportModal();
}

// 查看订单详情
function handleView(id?: number) {
  if (!id) return;
  window.$message?.info(`查看订单 ${id}`);
}

// 编辑订单
function handleEdit(id?: number) {
  if (!id) return;
  window.$message?.info(`编辑订单 ${id}`);
}

// 发货
function handleShip(id?: number) {
  if (!id) return;
  window.$dialog?.info({
    title: '确认发货',
    content: `确定要为订单 ${id} 进行发货操作吗？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      window.$message?.success(`订单 ${id} 发货成功`);
    }
  });
}

// 取消订单
function handleCancel(id?: number) {
  if (!id) return;
  window.$dialog?.warning({
    title: '确认取消',
    content: `确定要取消订单 ${id} 吗？此操作不可撤销。`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      window.$message?.success(`订单 ${id} 已取消`);
    }
  });
}

// 导出订单
function handleExport() {
  window.$message?.info('导出功能开发中...');
}

// 下载模板
function handleDownloadTemplate() {
  downloadTemplate().then(response => {
    if (response.error) {
      window.$message?.error('下载模板失败');
      return;
    }
    
    // 创建下载链接
    const url = window.URL.createObjectURL(response.data);
    const link = document.createElement('a');
    link.href = url;
    link.download = '订单导入模板.xlsx'; // 设置下载文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }).catch(error => {
    console.error('下载模板失败:', error);
    window.$message?.error('下载模板失败');
  });
}

// 批量设置标签
function handleBatchSetTags() {
  if (selectedOrderIds.value.length === 0) {
    window.$message?.warning('请先选择要设置标签的订单');
    return;
  }
  openBatchTagModal();
}

// 确认批量设置标签
function handleConfirmBatchTags() {
  if (batchSelectedTags.value.length === 0) {
    window.$message?.warning('请选择要设置的标签');
    return;
  }

  window.$message?.success(`已为 ${selectedOrderIds.value.length} 个订单设置标签`);

  // 重置状态
  selectedOrderIds.value = [];
  batchSelectedTags.value = [];
  closeBatchTagModal();
}

// 表格行选择
function handleRowSelection(rowKeys: Array<string | number>) {
  selectedOrderIds.value = rowKeys.map(key => String(key));
}

const orderManagement = ref<HTMLElement>();

// 页面初始化
onMounted(() => {
  getOrderList();
});
</script>

<template>
  <div ref="orderManagement" class="relative p-16px">
    <!-- 标签页 -->
    <NTabs v-model:value="activeTab" type="line" class="mb-16px" @update:value="handleTabChange">
      <NTabPane v-for="tab in tabOptions" :key="tab.key" :name="tab.key" :tab="tab.label" />
    </NTabs>

    <!-- 搜索表单 -->
    <NCard :bordered="false" class="mb-16px">
      <div class="mb-16px flex items-center justify-between">
        <h3 class="text-16px font-medium">筛选条件</h3>
        <NButton text @click="toggleSearchExpanded">
          <template #icon>
            <icon-mdi-chevron-up v-if="searchExpanded" />
            <icon-mdi-chevron-down v-else />
          </template>
          {{ searchExpanded ? '收起' : '展开' }}
        </NButton>
      </div>

      <NForm
        v-show="searchExpanded"
        :model="searchForm"
        label-placement="left"
        label-width="140px"
        :show-feedback="false"
      >
        <NGrid :cols="3" :x-gap="16" :y-gap="16">
          <!-- 时间筛选 -->
          <NFormItemGi label="订单提交时间">
            <NDatePicker
              v-model:value="searchForm.orderSubmitTimeRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="客户付款时间（中国）">
            <NDatePicker
              v-model:value="searchForm.customerPaymentTimeRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>

          <NFormItemGi label="截止发货时间（中国）">
            <NDatePicker
              v-model:value="searchForm.deliveryDeadlineRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="预计到货时间（中国）">
            <NDatePicker
              v-model:value="searchForm.expectedArrivalChinaRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>

          <NFormItemGi label="预计到货时间（美国）">
            <NDatePicker
              v-model:value="searchForm.expectedArrivalUSRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>

          <!-- 输入框筛选 -->
          <NFormItemGi label="商品名称">
            <NInput v-model:value="searchForm.productName" placeholder="请输入商品名称" clearable />
          </NFormItemGi>
          <NFormItemGi label="代拍商品ASIM">
            <NInput v-model:value="searchForm.proxyProductASIM" placeholder="请输入代拍商品ASIM" clearable />
          </NFormItemGi>

          <NFormItemGi label="经营平台订单号">
            <NInput
              v-model:value="searchForm.businessPlatformOrderNumber"
              placeholder="请输入经营平台订单号"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="运单号">
            <NInput v-model:value="searchForm.trackingNumber" placeholder="请输入运单号" clearable />
          </NFormItemGi>

          <NFormItemGi label="速峰订单号">
            <NInput v-model:value="searchForm.sufengOrderNumber" placeholder="请输入速峰订单号" clearable />
          </NFormItemGi>
          <NFormItemGi label="订单留言">
            <NInput v-model:value="searchForm.orderMessage" placeholder="请输入订单留言" clearable />
          </NFormItemGi>

          <NFormItemGi label="B单号">
            <NInput v-model:value="searchForm.bOrderNumber" placeholder="请输入B单号" clearable />
          </NFormItemGi>
          <NFormItemGi label="店铺名称">
            <NInput v-model:value="searchForm.storeName" placeholder="请输入店铺名称" clearable />
          </NFormItemGi>

          <!-- 选择器筛选 -->
          <NFormItemGi label="订单标签">
            <NSelect
              v-model:value="searchForm.orderTags"
              :options="orderTagOptions"
              placeholder="请选择订单标签"
              multiple
              clearable
              max-tag-count="responsive"
            />
          </NFormItemGi>
          <NFormItemGi label="是否有B单号">
            <NSelect
              v-model:value="searchForm.hasBOrderNumber"
              :options="hasBOrderNumberOptions"
              placeholder="请选择"
              clearable
            />
          </NFormItemGi>
        </NGrid>

        <!-- 操作按钮 -->
        <div class="mt-16px flex justify-end gap-12px">
          <NButton @click="handleReset">重置</NButton>
          <NButton type="primary" @click="handleSearch">搜索</NButton>
        </div>
      </NForm>
    </NCard>

    <!-- 操作栏 -->
    <div class="mb-16px flex items-center justify-between">
      <div class="flex gap-12px">
        <NButton type="primary" @click="handleAddOrder">
          <template #icon>
            <icon-ic-round-add />
          </template>
          新增订单
        </NButton>
        <NButton type="info" @click="handleBatchImport">
          <template #icon>
            <icon-mdi-upload />
          </template>
          批量导入订单
        </NButton>
        <NButton type="warning" :disabled="selectedOrderIds.length === 0" @click="handleBatchSetTags">
          <template #icon>
            <icon-mdi-tag-multiple />
          </template>
          批量设置标签 ({{ selectedOrderIds.length }})
        </NButton>
      </div>
      <div class="flex gap-12px">
        <NButton @click="handleExport">
          <template #icon>
            <icon-mdi-download />
          </template>
          导出
        </NButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <NCard :bordered="false" class="card-wrapper">
      <NDataTable
        :columns="columns"
        :data="orderList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        size="small"
        class="sm:h-full"
        scroll-x="1600"
        :row-key="(row: OrderData) => row.id || 0"
        @update:checked-row-keys="handleRowSelection"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </NCard>

    <!-- 新增订单弹出框 -->
    <NModal v-model:show="modalVisible" preset="card" title="新增订单" class="w-800px" :segmented="{ footer: 'soft' }">
      <div class="py-40px text-center">
        <NText depth="3">新增订单功能开发中...</NText>
      </div>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeModal">取消</NButton>
          <NButton type="primary" @click="closeModal">确认</NButton>
        </div>
      </template>
    </NModal>

    <!-- 批量导入弹出框 -->
    <NModal
      v-model:show="importModalVisible"
      preset="card"
      title="批量导入订单"
      class="w-600px"
      :segmented="{ footer: 'soft' }"
    >
      <div class="py-40px text-center">
        <NText depth="3">批量导入功能开发中...</NText>
      </div>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeImportModal">取消</NButton>
          <NButton type="primary" @click="closeImportModal">确认</NButton>
        </div>
      </template>
    </NModal>

    <!-- 批量设置标签弹出框 -->
    <NModal
      v-model:show="batchTagModalVisible"
      preset="card"
      title="批量设置标签"
      class="w-600px"
      :segmented="{ footer: 'soft' }"
    >
      <div class="mb-16px">
        <NText>已选择 {{ selectedOrderIds.length }} 个订单</NText>
      </div>

      <NFormItem label="选择标签">
        <NSelect
          v-model:value="batchSelectedTags"
          :options="orderTagOptions"
          placeholder="请选择要设置的标签"
          multiple
          clearable
          max-tag-count="responsive"
        >
          <template #header>
            <div class="flex gap-8px p-8px">
              <NButton size="small" @click="batchSelectedTags = orderTagOptions.map(o => o.value)">全选</NButton>
              <NButton size="small" @click="batchSelectedTags = []">清空</NButton>
              <NButton
                size="small"
                @click="
                  batchSelectedTags = orderTagOptions
                    .filter(o => !batchSelectedTags.includes(o.value))
                    .map(o => o.value)
                "
              >
                反选
              </NButton>
            </div>
          </template>
        </NSelect>
      </NFormItem>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeBatchTagModal">取消</NButton>
          <NButton type="primary" @click="handleConfirmBatchTags">确认设置</NButton>
        </div>
      </template>
    </NModal>

    <!-- 批量上传订单抽屉 -->
    <NDrawer
      v-model:show="batchUploadDrawerVisible"
      width="100%"
      placement="right"
      :show-mask="false"
      :mask-closable="false"
      :to="orderManagement"
    >
      <NDrawerContent title="批量上传订单" closable>
        <div class="h-full flex flex-col">
          <!-- 顶部操作区 -->
          <div class="mb-16px flex items-center justify-between">
            <div class="flex items-center gap-12px">
              <NButton type="primary">
                <template #icon>
                  <icon-mdi-upload />
                </template>
                上传订单文件
              </NButton>
              <NButton @click="handleDownloadTemplate">
                <template #icon>
                  <icon-mdi-download />
                </template>
                下载模板
              </NButton>
            </div>
            <div class="flex items-center gap-12px">
              <NButton type="success">
                <template #icon>
                  <icon-mdi-download />
                </template>
                导出记录
              </NButton>
            </div>
          </div>

          <!-- 标签页 -->
          <NTabs v-model:value="batchUploadActiveTab" type="line" class="flex-1">
            <NTabPane name="success" tab="识别成功">
              <div class="h-full">
                <NDataTable
                  :columns="successColumns"
                  :data="successOrderData"
                  :bordered="false"
                  size="small"
                  :pagination="{ pageSize: 20 }"
                  class="h-full"
                />
              </div>
            </NTabPane>
            <NTabPane name="failure" tab="识别失败">
              <div class="h-full">
                <NDataTable
                  :columns="failureColumns"
                  :data="failureOrderData"
                  :bordered="false"
                  size="small"
                  :pagination="{ pageSize: 20 }"
                  class="h-full"
                />
              </div>
            </NTabPane>
          </NTabs>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style lang="scss">
.card-wrapper {
  min-height: 500px;
}
</style>
